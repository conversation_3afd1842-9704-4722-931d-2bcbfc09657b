module GenerationTasks<PERSON><PERSON><PERSON>
  def generation_task_placeholder_title(task)
    if task.generation_params[:mode] == "parameters" || task.generation_params["mode"] == "parameters"
      # Use title from parameters mode
      title = task.generation_params[:title] || task.generation_params["title"]
      return title if title.present?

      # Fallback: generate title from composed style if available
      style = get_composed_style(task)
      if style.present?
        # Extract first few words from style as title
        style_words = style.split.first(3).join(" ")
        return style_words.length > 40 ? "#{style_words[0...37]}..." : style_words
      end

      "Untitled Song"
    else
      # Extract a title from the prompt
      prompt = task.generation_params[:prompt] || task.generation_params["prompt"] || ""
      if prompt.present?
        # Take first few words as title, max 50 chars
        title = prompt.split.first(6).join(" ")
        return title.length > 50 ? "#{title[0...47]}..." : title
      end

      "Generating Song"
    end
  end

  def generation_task_placeholder_description(task)
    if task.generation_params[:mode] == "parameters" || task.generation_params["mode"] == "parameters"
      parts = []

      # Add style information
      style = get_composed_style(task)
      parts << "Style: #{style}" if style.present?

      # Add instrumental indicator
      instrumental = task.generation_params[:instrumental] || task.generation_params["instrumental"]
      if instrumental == true || instrumental == "true"
        parts << "Instrumental"
      else
        parts << "With vocals"
      end

      # Add lyrics preview if available
      lyrics = task.generation_params[:lyrics] || task.generation_params["lyrics"]
      if lyrics.present? && !instrumental
        lyrics_preview = lyrics.split("\n").first || lyrics
        lyrics_preview = lyrics_preview.length > 60 ? "#{lyrics_preview[0...57]}..." : lyrics_preview
        parts << "Lyrics: \"#{lyrics_preview}\""
      end

      parts.join(" • ").presence || "Custom parameters"
    else
      # Description mode
      prompt = task.generation_params[:prompt] || task.generation_params["prompt"]
      if prompt.present?
        return prompt.length > 100 ? "#{prompt[0...97]}..." : prompt
      end

      "AI-generated music"
    end
  end

  def generation_task_placeholder_artist(task)
    # For parameters mode, show more specific info
    instrumental = task.generation_params[:instrumental]
    if instrumental == true || instrumental == "true"
      "Instrumental"
    else
      "With vocals"
    end
  end

  def generation_task_placeholder_genre(task)
    # Extract genre information from composed style or prompt
    if task.generation_params[:mode] == "parameters" || task.generation_params["mode"] == "parameters"
      style = get_composed_style(task)
      return style if style.present?
    else
      prompt = task.generation_params[:prompt] || task.generation_params["prompt"]
      # Try to extract genre keywords from prompt
      if prompt.present?
        genre_keywords = %w[rock pop jazz classical electronic hip-hop country blues reggae folk metal punk indie]
        found_genre = genre_keywords.find { |genre| prompt.downcase.include?(genre) }
        return found_genre.capitalize if found_genre
      end
    end

    "Various"
  end

  def generation_task_song_variant_info(task, song_index)
    # Provide meaningful variant information for each song in a generation task
    base_artist = generation_task_placeholder_artist(task)

    "#{base_artist} • ##{song_index + 1}"
  end

  def generation_task_placeholder_id(task, song_index)
    "generation_task_#{task.id}_song_#{song_index}"
  end

  private

  def get_composed_style(task)
    # First try to get the pre-composed style from generation_params
    style = task.generation_params[:style] || task.generation_params["style"]
    return style if style.present?

    # If no pre-composed style, try to compose from individual components
    params = task.generation_params
    genre = Array(params[:genre] || params["genre"]).select(&:present?)
    mood = Array(params[:mood] || params["mood"]).select(&:present?)
    vocal = params[:vocal] || params["vocal"]
    instrument = Array(params[:instrument] || params["instrument"]).select(&:present?)
    tpm = params[:tpm] || params["tpm"]

    # Use MusicStyleComposer to compose style if we have components
    if genre.any? || mood.any? || vocal.present? || instrument.any? || tpm.present?
      MusicStyleComposer.compose(
        genre: genre,
        mood: mood,
        vocal: vocal,
        instrument: instrument,
        tpm: tpm
      )
    else
      nil
    end
  end
end
