class SubscriptionWebhookService
  def initialize(event)
    @event = event
  end

  # Handle subscription status updates
  def handle_subscription_updated
    subscription_data = @event.data.object

    # Find local subscription by Stripe subscription ID
    subscription = find_subscription_by_stripe_id(subscription_data.id)

    unless subscription
      Rails.logger.error("❌ Subscription not found for Stripe ID: #{subscription_data.id}")
      return { success: false, error: "Subscription not found" }
    end

    Rails.logger.info("✅ Found subscription: #{subscription.id} for user: #{subscription.user.username}")

    # Update subscription status and period
    update_subscription_from_stripe!(subscription, subscription_data)

    # Update user plan type and credits based on subscription status
    update_user_plan_from_subscription!(subscription)

    { success: true }
  end

  # Handle subscription deletion/cancellation
  def handle_subscription_deleted
    subscription_data = @event.data.object

    # Find local subscription by Stripe subscription ID
    subscription = find_subscription_by_stripe_id(subscription_data.id)

    unless subscription
      Rails.logger.error("❌ Subscription not found for Stripe ID: #{subscription_data.id}")
      return { success: false, error: "Subscription not found" }
    end

    Rails.logger.info("✅ Found subscription: #{subscription.id} for user: #{subscription.user.username}")

    # Mark subscription as canceled
    subscription.update!(status: :canceled)

    # Downgrade user to free plan
    downgrade_user_to_free!(subscription.user)

    { success: true }
  end

  # Handle successful invoice payment (subscription renewal)
  def handle_invoice_payment_succeeded
    invoice_data = @event.data.object

    # Extract subscription ID from invoice data
    # For subscription invoices, Stripe provides the subscription ID directly in the subscription field
    subscription_id = invoice_data["subscription"]

    unless subscription_id
      Rails.logger.warn("⚠️ Invoice #{invoice_data.id} has no subscription ID, skipping")
      return { success: true }
    end

    # Find local subscription by Stripe subscription ID
    subscription = find_subscription_by_stripe_id(subscription_id)

    unless subscription
      Rails.logger.error("❌ Subscription not found for Stripe ID: #{subscription_id}")
      return { success: false, error: "Subscription not found" }
    end

    Rails.logger.info("✅ Found subscription: #{subscription.id} for user: #{subscription.user.username}")

    # Retrieve updated subscription data from Stripe
    stripe_subscription = Stripe::Subscription.retrieve(subscription_id)

    # Update subscription period and ensure it's active
    subscription.update!(
      status: map_stripe_status_to_local(stripe_subscription.status),
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end)
    )

    # Reset user's plan credits for the new period
    reset_user_plan_credits!(subscription.user)

    { success: true }
  end

  # Handle failed invoice payment (subscription renewal failure)
  def handle_invoice_payment_failed
    invoice_data = @event.data.object

    # Extract subscription ID from invoice data
    # For subscription invoices, Stripe provides the subscription ID directly in the subscription field
    subscription_id = invoice_data["subscription"]

    unless subscription_id
      Rails.logger.warn("⚠️ Invoice #{invoice_data.id} has no subscription ID, skipping")
      return { success: true }
    end

    # Find local subscription by Stripe subscription ID
    subscription = find_subscription_by_stripe_id(subscription_id)

    unless subscription
      Rails.logger.error("❌ Subscription not found for Stripe ID: #{subscription_id}")
      return { success: false, error: "Subscription not found" }
    end

    Rails.logger.info("✅ Found subscription: #{subscription.id} for user: #{subscription.user.username}")

    # Retrieve updated subscription data from Stripe to get current status
    stripe_subscription = Stripe::Subscription.retrieve(subscription_id)

    # Update subscription status (might be past_due, unpaid, etc.)
    subscription.update!(
      status: map_stripe_status_to_local(stripe_subscription.status)
    )

    # If subscription is past_due or unpaid, consider downgrading user
    if subscription.past_due? || subscription.unpaid?
      Rails.logger.warn("⚠️ Subscription #{subscription.id} payment failed, user may lose Pro benefits")
      # Note: We might want to give users a grace period before downgrading
      # For now, we'll keep them as Pro but log the issue
    end

    { success: true }
  end

  private

  def find_subscription_by_stripe_id(stripe_subscription_id)
    Subscription.find_by(stripe_subscription_id: stripe_subscription_id)
  end

  def update_subscription_from_stripe!(subscription, stripe_data)
    subscription.update!(
      status: map_stripe_status_to_local(stripe_data.status),
      current_period_start: Time.at(stripe_data.current_period_start),
      current_period_end: Time.at(stripe_data.current_period_end)
    )
  end

  def update_user_plan_from_subscription!(subscription)
    user = subscription.user

    if subscription.active? || subscription.trialing?
      # User should have Pro benefits
      user.update!(
        plan_type: :pro,
        plan_credits_limit: subscription.plan.plan_limit || 0
      )
      Rails.logger.info("✅ Updated user #{user.username} to Pro plan")
    else
      # Subscription is not active, downgrade to free
      downgrade_user_to_free!(user)
    end
  end

  def downgrade_user_to_free!(user)
    # Get free plan credits limit from PlanConfiguration
    free_credits_limit = user.class.free_plan_credits_limit

    user.update!(
      plan_type: :free,
      plan_credits_limit: free_credits_limit,
      plan_credits_used: 0  # Reset credits when downgrading
    )

    Rails.logger.info("✅ Downgraded user #{user.username} to free plan")
  end

  def reset_user_plan_credits!(user)
    user.reset_plan_credits!
    Rails.logger.info("✅ Reset plan credits for user #{user.username}")
  end

  def map_stripe_status_to_local(stripe_status)
    case stripe_status
    when "active"
      :active
    when "canceled"
      :canceled
    when "past_due"
      :past_due
    when "unpaid"
      :unpaid
    when "incomplete"
      :incomplete
    when "incomplete_expired"
      :incomplete_expired
    when "trialing"
      :trialing
    else
      Rails.logger.warn("⚠️ Unknown Stripe subscription status: #{stripe_status}")
      :canceled  # Default to canceled for unknown statuses
    end
  end
end
