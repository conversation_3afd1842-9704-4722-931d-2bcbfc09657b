class GenerationTask < ApplicationRecord
  include PlanConfiguration

  # Associations
  belongs_to :user
  has_many :songs, dependent: :destroy

  # Enums
  enum :status, {
    pending: 0,
    # lyric/text generation is complete, but none of the audio generations have completed
    # Stream audio is available for both songs however
    generating_first_audio: 1,
    generating_last_audio: 2, # first audio generation is complete, other audio generations are in progress
    retrying: 3, # the generation failed, and is being retried
    succeeded: 4, # both songs are complete, with audio_url available
    failed: 5
  }

  # Callbacks
  before_create :set_timeout_at
  after_create :queue_generation_job

  # Validations
  validates :status, presence: true
  validates :api_version, presence: true
  validates :generation_params, presence: true

  # Status-based validations
  validates :task_id, presence: true, if: :requires_task_data?
  validates :task_data, presence: true, if: :requires_task_data?
  validates :completed_at, presence: true, if: :completed?
  validates :completed_at, absence: true, if: :in_progress?
  validates :timeout_at, presence: true, if: :requires_timeout_at?
  validates :timeout_at, absence: true, if: :completed?

  # Scopes
  scope :completed, -> { where(status: [ :succeeded, :failed ]) }
  scope :in_progress, -> { where(status: [ :pending, :generating_first_audio, :generating_last_audio, :retrying ]) }
  scope :queued, -> { where(status: [ :pending, :retrying ]) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_api_version, ->(api_version) { where(api_version: api_version) }
  # Order by generation priority: in_progress tasks first, then completed, all by most recent
  # Rails-native approach using enum values for ordering
  scope :order_by_generation_priority, -> {
    # Use status enum directly - in_progress statuses (0,1,2,3) will naturally sort before completed statuses (4,5)
    # We want in_progress first, so we order by the boolean expression (status IN completed_statuses) ASC
    completed_statuses = statuses.values_at(:succeeded, :failed)
    status_in_completed = arel_table[:status].in(completed_statuses)

    order(status_in_completed.asc, created_at: :desc)
  }

  # Methods
  def completed?
    succeeded? || failed?
  end

  def in_progress?
    pending? || generating_first_audio? || generating_last_audio? || retrying?
  end

  def mark_completed!(timestamp = Time.current)
    update!(completed_at: timestamp)
  end

  def duration_seconds
    return nil unless completed_at && created_at
    completed_at - created_at
  end

  # Helper methods for generation parameters
  def should_generate_timestamped_lyrics?
    # 只有非纯音乐且有歌词的情况下才生成时间戳歌词
    !instrumental?
  end

  def instrumental?
    # 检查生成参数中是否标明为纯音乐
    generation_params["instrumental"] == true || generation_params[:instrumental] == true
  end

  def lyrics_from_params
    if generation_params[:mode] == "parameters"
      return generation_params[:lyrics] || ""
    end
    ""
  end

  # Update task status from webhook callback
  def update_from_webhook!(payload, new_status)
    update_attributes = {
      task_data: payload,
      status: new_status
    }

    # Mark as completed if final state
    if [ :succeeded, :failed ].include?(new_status)
      update_attributes[:completed_at] = Time.current
      update_attributes[:timeout_at] = nil # Clear timeout when task completes
    end

    # Handle errors
    if new_status == :failed
      error_message = payload["msg"] || payload.dig("data", "error") || "任务失败"
      update_attributes[:error_data] = {
        callback_type: payload.dig("data", "callbackType"),
        message: error_message,
        failed_at: Time.current,
        full_payload: payload
      }
    else
      update_attributes[:error_data] = nil # Clear any previous errors
    end

    update!(update_attributes)

    # Schedule lyrics generation for all songs only after successful completion
    if new_status == :succeeded && should_generate_timestamped_lyrics?
      schedule_lyrics_generation_for_all_songs
    end
  end

  # Schedule lyrics generation for all songs associated with this task
  def schedule_lyrics_generation_for_all_songs
    Rails.logger.info "🎤 安排任务所有歌曲的歌词生成: 任务ID=#{id}, 任务task_id=#{task_id}"

    songs.each do |song|
      song.schedule_lyrics_generation
    end
  end

  # Broadcast status update to user via Turbo Streams
  def broadcast_status_update
    Rails.logger.info "📡 广播任务状态更新: 任务ID=#{id}, 状态=#{status}, 用户ID=#{user.id}"

    Turbo::StreamsChannel.broadcast_render_to(
      user,
      :generation_task_updates,
      partial: "generations/update_status",
      locals: { task: self }
    )
  end

  # Class method to create generation with credit validation and transaction
  def self.create_with_credits!(user, raw_params)
    unless user.can_generate_music?
      raise InsufficientCreditsError, "Insufficient credits for generation"
    end

    # Validate and clean parameters
    cleaned_params = validate_and_clean_params(raw_params)

    result = nil
    user.transaction do
      # Deduct credits first
      user.spend_credits!(user.generation_cost)

      # Create generation task
      task = create!(
        user: user,
        api_version: "apibox_suno",
        generation_params: cleaned_params,
        status: :pending
      )

      Rails.logger.info "Created generation task #{task.id} for user #{user.id}"
      result = task
    end

    result
  end

  # Validate and clean generation parameters
  def self.validate_and_clean_params(params)
    # Extract and permit parameters
    cleaned = extract_permitted_params(params)

    # Set default values
    set_defaults(cleaned)

    # Validate based on mode
    validate_by_mode(cleaned)

    # Apply character limits and clean data
    apply_limits_and_clean(cleaned)

    cleaned
  end

  private

  # Task data is required when processing or succeeded
  def requires_task_data?
    generating_first_audio? || generating_last_audio? || succeeded?
  end

  # Timeout is required for in-progress tasks (set during creation via before_create callback)
  # But not during initial creation when the record is still new
  def requires_timeout_at?
    in_progress? && persisted?
  end

  # Queue the generation job after creation
  def queue_generation_job
    GenerationCreateJob.perform_later(self)
  end

  # Set timeout_at before creation
  def set_timeout_at
    timeout_minutes = ENV.fetch("GENERATION_TIMEOUT_MINUTES", "15").to_i
    self.timeout_at = timeout_minutes.minutes.from_now

    Rails.logger.info "⏰ 设置超时时间: 任务创建中, 超时时间=#{timeout_at}, 超时分钟数=#{timeout_minutes}"
  end

  # Parameter validation helper methods
  def self.extract_permitted_params(params)
    # Allow both string and array values for genre, mood, instrument
    permitted_params = params.require(:generation).permit(
      :mode, :prompt, :lyrics, :title, :instrumental, :request_id,
      :vocal, :vocal_type, :tpm, genre: [], mood: [], instrument: []
    ).to_h

    Rails.logger.info "🔍 Raw permitted params: #{permitted_params.inspect}"

    # Ensure array fields are always arrays
    %w[genre mood instrument].each do |field|
      value = permitted_params[field.to_sym]
      if value.nil?
        permitted_params[field.to_sym] = []
      elsif value.is_a?(String)
        permitted_params[field.to_sym] = value.present? ? [ value ] : []
      elsif value.is_a?(Array)
        permitted_params[field.to_sym] = value.select(&:present?)
      else
        permitted_params[field.to_sym] = []
      end
    end

    # Handle vocal_type parameter and map to instrumental/vocal
    vocal_type = permitted_params[:vocal_type].presence
    if vocal_type == "instrumental"
      permitted_params[:instrumental] = true
      permitted_params[:vocal] = nil
    elsif %w[male female].include?(vocal_type)
      permitted_params[:instrumental] = false
      permitted_params[:vocal] = vocal_type
    else
      # Default to male vocals if no vocal_type specified
      permitted_params[:instrumental] = false
      permitted_params[:vocal] = nil
    end

    # Ensure single-value fields have proper fallbacks
    permitted_params[:tpm] = permitted_params[:tpm].presence

    Rails.logger.info "🔍 Processed params: #{permitted_params.inspect}"
    permitted_params
  end

  def self.set_defaults(cleaned)
    cleaned[:mode] ||= "description"

    # Instrumental is already set by vocal_type processing, but ensure it's boolean
    cleaned[:instrumental] = !!cleaned[:instrumental]

    # Set fallback values for advanced options
    cleaned[:tpm] ||= nil
    cleaned[:genre] ||= []
    cleaned[:mood] ||= []
    cleaned[:instrument] ||= []
  end

  def self.validate_by_mode(cleaned)
    if cleaned[:mode] == "parameters"
      validate_parameters_mode(cleaned)
    else
      validate_description_mode(cleaned)
    end
  end

  def self.validate_parameters_mode(cleaned)
    # Check if we have required basic components (genre or mood are required)
    # Advanced options (vocal, instrument, tpm) are optional
    has_genre = cleaned[:genre].is_a?(Array) && cleaned[:genre].any?(&:present?)
    has_mood = cleaned[:mood].is_a?(Array) && cleaned[:mood].any?(&:present?)

    # At least genre or mood is required for parameters mode
    unless has_genre || has_mood
      raise ArgumentError, "At least one genre or mood is required for parameters mode"
    end

    if cleaned[:title].blank?
      raise ArgumentError, "Title is required for parameters mode"
    end

    # If not instrumental, lyrics is required
    if !cleaned[:instrumental] && cleaned[:lyrics].blank?
      raise ArgumentError, "Lyrics is required when not instrumental"
    end
  end

  def self.validate_description_mode(cleaned)
    if cleaned[:prompt].blank?
      raise ArgumentError, "Prompt is required for description mode"
    end
  end

  def self.apply_limits_and_clean(cleaned)
    if cleaned[:mode] == "parameters"
      # Apply character limits for parameters mode
      cleaned[:title] = cleaned[:title][0...80] if cleaned[:title].present?
      cleaned[:lyrics] = cleaned[:lyrics][0...3000] if cleaned[:lyrics].present?

      # Always compose style from components (even if some are empty/nil)
      # MusicStyleComposer.compose handles nil/empty values gracefully
      cleaned[:style] = MusicStyleComposer.compose(
        genre: cleaned[:genre] || [],
        mood: cleaned[:mood] || [],
        vocal: cleaned[:vocal],
        instrument: cleaned[:instrument] || [],
        tpm: cleaned[:tpm]
      )

      # Clear prompt field for parameters mode
      cleaned[:prompt] = nil
    else
      # Apply character limit for description mode
      cleaned[:prompt] = cleaned[:prompt][0...400] if cleaned[:prompt].present?

      # Clear other fields for description mode
      cleaned[:style] = nil
      cleaned[:title] = nil
      cleaned[:lyrics] = nil
      cleaned[:genre] = nil
      cleaned[:mood] = nil
      cleaned[:vocal] = nil
      cleaned[:instrument] = nil
      cleaned[:tpm] = nil
    end
  end
end
