class SessionsController < ApplicationController
  allow_unauthenticated_access only: %i[ new request_otp verify_otp google google_callback auth_failure ]
  rate_limit to: 10, within: 3.minutes, only: [ :request_otp, :verify_otp ], with: -> { redirect_to new_session_url, alert: "Try again later." }

  # Render login page
  def new
  end

  # Handle logout request
  def destroy
    terminate_session
    redirect_to new_session_path
  end

  # Request OTP, use turbo_stream response to request OTP verification form
  def request_otp
    request_params = email_otp_params
    otp_data = OtpService.generate(request_params[:email])

    # Send OTP email in production, log to console in development
    if Rails.env.production?
      OtpMailer.otp(request_params[:email], otp_data[:otp]).deliver_now
    else
      Rails.logger.info "[OTP] email: #{request_params[:email]}, otp: #{otp_data[:otp]}"
    end

    session[:otp_token] = otp_data[:token]
  rescue Postmark::InactiveRecipientError => e
    Rails.logger.error "MailerError: #{e.message}"
    flash.now[:alert] = "Failed to send OTP to the email address, please check the email address."
  rescue => e
    Rails.logger.error "OtpError: #{e.message}"
    flash.now[:alert] = "Failed to send OTP, please try again later."
  end

  def verify_otp
    verify_params = email_otp_verify_params
    if OtpService.verify(**verify_params)
      user, _is_new = User.find_or_create_from_email_otp(verify_params[:email])
      start_new_session_for user
      redirect_to after_authentication_url
    else
      flash.now[:alert] = "Invalid or expired OTP."
    end
  rescue OtpService::OtpVerificationError => e
    # TODO: sentry
    Rails.logger.error "OtpVerificationError: #{e.message}"
    flash.now[:alert] = "Invalid or expired OTP."
  end

  # Handle google oauth2 request
  def google
    session[:next_url] = oauth_params[:next_url]
    redirect_to "/auth/google_oauth2"
  end

  # Handle google oauth2 callback
  def google_callback
    auth_hash = request.env["omniauth.auth"]

    if auth_hash.blank?
      Rails.logger.error "OAuth callback: No auth hash received"
      redirect_to new_session_path, alert: "Authentication failed. Please try again."
      return
    end

    user, _is_new = User.from_omniauth(auth_hash)

    if user.persisted?
      start_new_session_for user
      next_url = get_next_url()
      if next_url.present?
        remove_next_url
        redirect_to next_url
      else
        redirect_to after_authentication_url
      end
    else
      Rails.logger.error "OAuth callback: Failed to create/find user from auth hash: #{auth_hash.inspect}"
      Rails.logger.error "User errors: #{user.errors.full_messages}" if user.respond_to?(:errors)
      redirect_to new_session_path, alert: "Account creation failed. Please try again."
    end
  rescue => e
    Rails.logger.error "OAuth callback error: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    redirect_to new_session_path, alert: "Authentication failed. Please try again."
  end


  # Handle OAuth authentication failures
  def auth_failure
    error_message = case params[:message]
    when "access_denied"
      "Authentication was cancelled."
    when "invalid_credentials"
      "Invalid credentials provided."
    when "timeout"
      "Authentication timed out. Please try again."
    when "csrf_detected"
      "Security error detected. Please try again."
    when "invalid_request"
      "Invalid authentication request."
    else
      "Authentication failed. Please try again."
    end

    Rails.logger.warn "OAuth failure: #{params[:message]} - #{params[:strategy]}"

    redirect_to new_session_path, alert: error_message
  end

  private

  def email_otp_params
    email = params[:email]&.strip
    raise ActionController::ParameterMissing, "param is missing or the value is empty or invalid: email" if email.blank?
    { email: email }
  end

  def email_otp_verify_params
    email = params[:email]&.strip
    otp_pin = params[:otp_pin]&.strip

    raise ActionController::ParameterMissing, "param is missing or the value is empty or invalid: email" if email.blank?
    raise ActionController::ParameterMissing, "param is missing or the value is empty or invalid: otp_pin" if otp_pin.blank?
    raise "OTP token not found" if session[:otp_token].blank?

    {
      email: email,
      otp_pin: otp_pin,
      token: session[:otp_token]
    }
  end

  def oauth_params
    next_url = params[:next_url]

    # Only allow relative URLs for 'next' for security
    if next_url.present? && !next_url.start_with?("/")
      raise ActionController::ParameterMissing, "'next' must be a relative path"
    end

    { next_url: next_url }
  end

  def get_next_url
    next_url = session[:next_url]
    if next_url.present? && !next_url.start_with?("/")
      Rails.logger.error "Invalid next_url: #{next_url}"
      return nil
    end
    next_url
  end

  def remove_next_url
    session.delete(:next_url)
  end
end
