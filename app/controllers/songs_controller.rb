class SongsController < ApplicationController
  include PlanConfiguration
  include ApplicationHelper

  before_action :set_song, only: [ :show, :edit, :update, :toggle_favorite, :destroy ]

  def index
    # Get songs for the current user, sorted with generating/pending first, then by most recent
    # Using Rails-native scope approach with merge
    @songs = Current.user.songs
                    .joins(:generation_task)
                    .includes(:generation_task)
                    .merge(GenerationTask.order_by_generation_priority)

    # Get generation tasks that don't have songs yet (placeholders for generating tasks)
    @generating_tasks = Current.user.generation_tasks
                               .queued
                               .order(created_at: :desc)

    # Get failed generation tasks (to show failed items)
    @failed_tasks = Current.user.generation_tasks
                           .failed
                           .order(created_at: :desc)

    # Generation form variables (moved from generations#new)
    @current_credits = Current.user.spendable_credits
    @generation_cost = generation_cost
    @request_id = generate_request_id
  end

  def show
    # Song is set by before_action
  end

  def edit
    # Song is set by before_action
    # Return edit form in modal via Turbo Frame
  end

  def update
    if @song.update_title(song_params[:title])
      respond_to do |format|
        format.html do
          redirect_to @song, notice: "Song title updated successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "Song title updated successfully!" }
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def toggle_favorite
    if @song.toggle_favorite
      respond_to do |format|
        format.html do
          redirect_to @song, notice: @song.favorite? ? "#{@song.title} added to favorites!" : "#{@song.title} removed from favorites!"
        end
        format.turbo_stream { flash.now[:notice] = @song.favorite? ? "#{@song.title} added to favorites!" : "#{@song.title} removed from favorites!" }
      end
    else
      render :show, status: :unprocessable_entity
    end
  end

  def destroy
    if @song.destroy
      respond_to do |format|
        format.html do
          redirect_to songs_path, notice: "#{@song.title} deleted successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "#{@song.title} deleted successfully!" }
      end
    else
      redirect_to songs_path, alert: "Unable to delete song."
    end
  end

  private

  def set_song
    @song = Current.user.songs.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to songs_path, alert: "Song not found."
  end

  def song_params
    params.require(:song).permit(:title)
  end
end
