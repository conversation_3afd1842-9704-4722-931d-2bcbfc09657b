class GenerationsController < ApplicationController
  include PlanConfiguration
  include ActionController::Live
  include ApplicationHelper

  before_action :set_generation_task, only: [ :destroy ]
  before_action :check_credits, only: [ :create ]
  before_action :set_request_id, only: [ :new, :create ]

  def new
    @current_credits = Current.user.spendable_credits
    @generation_cost = generation_cost
  end

  def create
    Rails.logger.info "🎵 Generation create request: #{params[:generation]&.inspect}"

    begin
      @generation_task = GenerationTask.create_with_credits!(Current.user, params)

      respond_to do |format|
        format.html do
          redirect_to songs_path,
                      notice: "Your music generation has been started! You'll receive an update when it's ready."
        end
        format.turbo_stream { flash.now[:notice] = "Your music generation has been started! You'll receive an update when it's ready." }
      end
    rescue InsufficientCreditsError
      handle_generation_error("You don't have enough credits to generate music. Please upgrade your plan or purchase more credits.")
    rescue ActionController::ParameterMissing => e
      Rails.logger.error "Parameter missing: #{e.message}"
      handle_generation_error("Missing required parameters: #{e.message}")
    rescue ActionController::UnpermittedParameters => e
      Rails.logger.error "Unpermitted parameters: #{e.message}"
      handle_generation_error("Invalid parameters provided: #{e.message}")
    rescue ArgumentError => e
      Rails.logger.error "Argument error: #{e.message}"
      handle_generation_error(e.message)
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Record invalid: #{e.record.errors.full_messages}"
      handle_generation_error("Validation failed: #{e.record.errors.full_messages.join(', ')}")
    rescue => e
      Rails.logger.error "Generation creation failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      handle_generation_error("Something went wrong. Please try again.")
    end
  end

  def random_prompts
    prompts = [
      "A peaceful morning coffee shop ambiance with soft jazz",
      "An epic orchestral battle theme with dramatic crescendos",
      "A nostalgic 80s synthwave track with neon vibes",
      "A gentle acoustic folk song about wandering through forests",
      "An upbeat pop anthem about chasing dreams",
      "A melancholic piano ballad about lost love",
      "A high-energy electronic dance track for late-night parties",
      "A country song about small-town life and simple pleasures",
      "A classical string quartet piece with elegant melodies",
      "A reggae-inspired track with laid-back island vibes",
      "A rock anthem about breaking free from limitations",
      "A minimalist ambient piece for meditation and relaxation",
      "A funky disco track that makes you want to dance",
      "A hip-hop beat with smooth jazz samples",
      "A Celtic-inspired instrumental with traditional instruments"
    ]

    @prompts = prompts.sample(3)
  end

  def destroy
    if @generation_task.destroy
      respond_to do |format|
        format.html do
          redirect_to songs_path, notice: "Failed generation task deleted successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "Failed generation task deleted successfully!" }
      end
    else
      redirect_to songs_path, alert: "Unable to delete generation task."
    end
  end

  def search_presets
    field_type = params[:field_type]
    query = params[:query]
    request_id = params[:request_id]

    # Validate field type
    unless %w[genre mood instrument vocal tpm].include?(field_type)
      return head :bad_request
    end

    # Use stable recommendations per request when no search query is present
    if query.blank? && request_id.present?
      @suggestions = MusicPresets.stable_recommendations(field_type, request_id)
    else
      @suggestions = MusicPresets.search(field_type, query)
    end

    @field_type = field_type
    @query = query

    respond_to do |format|
      format.turbo_stream
      format.html { head :not_acceptable }
    end
  end

  def generate_lyrics
    # Extract parameters from form submission and compose style from components
    generation_params = params[:generation] || {}

    # Handle vocal_type parameter and convert to vocal
    vocal_type = generation_params[:vocal_type].presence
    vocal_param = case vocal_type
    when "instrumental"
                    nil
    when "male", "female"
                    vocal_type
    else
                    generation_params[:vocal].presence
    end

    # Compose style from individual components with proper fallbacks
    style = MusicStyleComposer.compose(
      genre: Array(generation_params[:genre]).select(&:present?),
      mood: Array(generation_params[:mood]).select(&:present?),
      vocal: vocal_param,
      instrument: Array(generation_params[:instrument]).select(&:present?),
      tpm: generation_params[:tpm].presence
    )

    request_id = generation_params[:request_id]
    target_id = "generation_lyrics"

    # Validate request ID is present
    unless request_id.present?
      Rails.logger.error "❌ Lyrics generation failed: Missing request ID"
      return render_lyrics_error("Invalid request. Please refresh the page and try again.")
    end

    begin
      Rails.logger.info "🎵 Starting lyrics generation: style=#{style}, request_id=#{request_id}"

      # Start LLM streaming job
      LlmStreamJob.perform_later(
        Current.user.id,
        "lyrics",
        target_id,
        request_id: request_id,
        style: style
      )

      respond_to do |format|
        format.turbo_stream do
          render turbo_stream: turbo_stream.update(:lyrics_gen_button, partial: "shared/lyrics_generation_button_loading")
        end
        format.html { head :accepted }
      end

    rescue => e
      Rails.logger.error "❌ Lyrics generation failed: #{e.message}"
      render_lyrics_error("Failed to generate lyrics. Please try again.")
    end
  end

  private

  def set_generation_task
    @generation_task = Current.user.generation_tasks.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to songs_path, alert: "Generation task not found."
  end

  def check_credits
    unless Current.user.can_generate_music?
      redirect_to new_generation_path,
                  alert: "You need at least #{generation_cost} credits to generate music."
    end
  end

  def handle_generation_error(error_message)
    @error_message = error_message
    respond_to do |format|
      format.html do
        redirect_to new_generation_path, alert: error_message
      end
      format.turbo_stream do
        flash.now[:alert] = error_message
      end
    end
  end

  def render_lyrics_error(error_message)
    respond_to do |format|
      format.turbo_stream do
        render turbo_stream: turbo_stream.update(:lyrics_gen_button, partial: "shared/lyrics_generation_button_submit")
      end
      format.html { head :unprocessable_entity }
    end
  end

  def set_request_id
    @request_id = generate_request_id
  end
end
