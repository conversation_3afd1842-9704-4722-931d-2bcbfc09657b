class UsersController < ApplicationController
  before_action :set_user, only: [ :show, :edit, :update ]
  before_action :check_owner, only: [ :show, :edit, :update ]
  before_action :require_authentication, only: [ :profile, :edit_profile, :update_profile ]

  def show
    # Show user profile (private - only viewable by the profile owner)
  end

  def edit
    # Edit user profile form
  end

  def update
    if @user.update(user_params)
      redirect_to user_profile_path(@user.username), notice: "Profile was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # Current user profile routes
  def profile
    @user = Current.user
    render :show
  end

  def edit_profile
    @user = Current.user
    render :edit
  end

  def update_profile
    @user = Current.user
    if @user.update(user_params)
      redirect_to profile_path, notice: "Profile was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_user
    @user = User.find_by!(username: params[:username])
  end

  def check_owner
    unless Current.user == @user
      redirect_to root_path, alert: "You can only view your own profile."
    end
  end

  def user_params
    # Note: email is intentionally not permitted - emails are immutable after account creation
    params.require(:user).permit(:username)
  end
end
