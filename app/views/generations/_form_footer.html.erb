        <!-- Submit Section -->
<div class="pt-3 border-t border-gray-200">
  <div class="text-center mb-3">
    <div class="text-xs text-gray-500">Generation Cost</div>
    <div class="text-sm font-semibold text-gray-900"><%= generation_cost %> Credits</div>
    <div class="text-xs text-gray-500">
      <% remaining_credits = current_credits - generation_cost %>
      <% if remaining_credits >= 0 %>
        <%= remaining_credits %> remaining after generation
      <% else %>
        <span class="text-red-600">Insufficient credits</span>
      <% end %>
    </div>
  </div>
  <div class="space-y-2">
    <% if current_credits < generation_cost %>
      <%= link_to plans_path, 
                          class: "w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block text-sm" do %>
        Get More Credits
      <% end %>
    <% end %>
    <%= form.submit "Generate Music", 
              disabled: current_credits < generation_cost,
              class: "w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm" %>
  </div>
</div>