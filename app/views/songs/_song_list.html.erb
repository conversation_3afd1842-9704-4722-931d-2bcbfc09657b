<ul role="list" class="divide-y divide-gray-200 peer" id="songs_list">
  <!-- Generation Task Placeholders (2 per task) -->
  <% @generating_tasks.each do |task| %>
    <% 2.times do |index| %>
      <%= render "generations/placeholder", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Failed Generation Tasks (2 per task) -->
  <% @failed_tasks.each do |task| %>
    <% 2.times do |index| %>
      <%= render "generations/failed_list_item", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Actual Songs -->
  <% @songs.each do |song| %>
    <%= render "song_list_item", song: song %>
  <% end %>
</ul>
<div class="p-6 text-center flex-1 flex peer-has-[li]:hidden flex-col justify-center">
  <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
    <%= flowbite_icon('music-outline', class: 'size-8 text-gray-400') %>
  </div>
  <h3 class="text-lg font-medium text-gray-900 mb-2">No songs yet</h3>
  <p class="text-gray-500">Start creating your first AI-generated song using the generation form</p>
</div>