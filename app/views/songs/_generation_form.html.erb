<%= turbo_frame_tag "generation_form" do %>
  <%= turbo_stream_from Current.user, llm_stream_channel_id(@request_id) %>
  <div class="p-4" data-controller="mode-switcher" data-mode-switcher-current-value="description">
    <!-- Credits Display -->
    <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
      <div class="text-center">
        <div class="text-sm text-blue-600 font-medium">Available Credits</div>
        <div class="text-xl font-bold text-blue-700"><%= @current_credits %></div>
        <div class="text-xs text-blue-600">Cost: <%= @generation_cost %> credits per generation</div>
      </div>
    </div>
    <!-- Mode Switcher -->
    <div class="mb-4">
      <div class="flex space-x-1 bg-gray-100 rounded-lg p-1 w-full">
        <button type="button" 
                data-action="click->mode-switcher#switchToDescription"
          data-mode-switcher-target="descriptionTab"
          class="flex-1 px-3 py-2 rounded-md text-xs font-medium transition-all duration-200 bg-blue-600 text-white shadow-sm">
          Simple
        </button>
        <button type="button" 
                data-action="click->mode-switcher#switchToParameters"
          data-mode-switcher-target="parametersTab"
          class="flex-1 px-3 py-2 rounded-md text-xs font-medium transition-all duration-200 text-gray-700 hover:text-gray-900 hover:bg-gray-200">
          Advanced
        </button>
      </div>
      <div class="mt-2 text-gray-600 text-xs">
        <span data-mode-switcher-target="descriptionHelp">
          💡 Describe the music you want in natural language
        </span>
        <span data-mode-switcher-target="parametersHelp" class="hidden">
          🎛️ Fine-tune specific musical parameters
        </span>
      </div>
    </div>
    <!-- Description Mode Form -->
    <%= render "generations/description_mode_form", 
        request_id: @request_id, 
        current_credits: @current_credits, 
        generation_cost: @generation_cost %>
    <!-- Parameters Mode Form -->
    <%= render "generations/parameters_mode_form", 
        request_id: @request_id, 
        current_credits: @current_credits, 
        generation_cost: @generation_cost %>
  </div>
<% end %>
