class FreeCreditResetJob < ApplicationJob
  queue_as :daily_tasks

  def perform
    Rails.logger.info "🔄 开始执行每日积分重置任务"

    reset_free_users_credits

    Rails.logger.info "✅ 每日积分重置任务完成"
  end

  private

  def reset_free_users_credits
    Rails.logger.info "🆓 开始重置免费用户积分"

    # 查找需要重置积分的免费用户
    free_users_query = User.free.needs_credit_reset
    total_count = free_users_query.count

    Rails.logger.info "📊 找到 #{total_count} 个免费用户需要重置积分"

    if total_count == 0
      Rails.logger.info "ℹ️ 没有免费用户需要重置积分"
      return
    end

    begin
      updated_count = free_users_query.update_all(
        plan_credits_used: 0,
        updated_at: Time.current
      )

      Rails.logger.info "✅ 免费用户积分重置完成: 成功重置 #{updated_count} 个用户的积分"
    rescue => e
      Rails.logger.error "❌ 批量重置免费用户积分失败: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end
end
