import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="auto-resize"
export default class extends Controller {
  static values = { maxHeight: Number };

  connect() {
    // Wait for next tick to ensure element is fully mounted
    requestAnimationFrame(() => {
      this.resize();
    });

    // Store bound function references so they can be removed later
    this.boundResize = this.resize.bind(this);
    this.element.addEventListener("input", this.boundResize);

    // Handle form reset events
    const form = this.element.closest("form");
    if (form) {
      this.boundHandleFormReset = this.handleFormReset.bind(this);
      form.addEventListener("reset", this.boundHandleFormReset);
    }
  }

  disconnect() {
    // Clean up event listeners
    this.element.removeEventListener("input", this.boundResize);

    const form = this.element.closest("form");
    if (form && this.boundHandleFormReset) {
      form.removeEventListener("reset", this.boundHandleFormReset);
    }
  }

  handleFormReset() {
    // Use setTimeout to ensure the form values are reset before resizing
    setTimeout(() => {
      this.resize();
    }, 0);
  }

  resize() {
    // Check if element is mounted to DOM
    if (!this.element.isConnected) {
      return;
    }

    // Store current scroll position to maintain it
    const scrollTop = this.element.scrollTop;

    // Reset height to auto to get accurate scrollHeight
    this.element.style.height = "auto";

    // Get the scrollHeight, but ensure minimum height
    const scrollHeight = this.element.scrollHeight;
    const minHeight = this.getMinHeight();
    const maxHeight = this.getMaxHeight();

    // Set height to the appropriate value considering min and max constraints
    let newHeight = Math.max(scrollHeight, minHeight);
    if (maxHeight > 0) {
      newHeight = Math.min(newHeight, maxHeight);
    }

    this.element.style.height = `${newHeight}px`;

    // Restore scroll position
    this.element.scrollTop = scrollTop;
  }

  getMinHeight() {
    // Calculate minimum height based on rows attribute or default
    const rows = Number.parseInt(this.element.getAttribute("rows") || "3");
    const lineHeight = Number.parseInt(
      window.getComputedStyle(this.element).lineHeight || "20",
    );
    const padding = this.getPadding();

    return rows * lineHeight + padding;
  }

  getMaxHeight() {
    // Return max height from data value or 0 (no limit)
    return this.hasMaxHeightValue ? this.maxHeightValue : 0;
  }

  getPadding() {
    const styles = window.getComputedStyle(this.element);
    const paddingTop = Number.parseInt(styles.paddingTop || "0");
    const paddingBottom = Number.parseInt(styles.paddingBottom || "0");
    return paddingTop + paddingBottom;
  }
}
